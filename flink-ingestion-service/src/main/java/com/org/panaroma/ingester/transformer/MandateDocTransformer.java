package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.VALIDITY_START_DATE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.WebConstants.RRN;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXCEPTION_WHILE_CREATING_MANDATE_DOC;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXCEPTION_WHILE_GETTING_PARENT_MANDATE_DOC;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.MANDATE_BACK_FILLING_EVENT_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PARENT_DOC_MISSING_FOR_PORT_OUT_EVENT;

import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.mandate.MandateBaseDto;
import com.org.panaroma.commons.dto.mandate.MandateInfoData;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.ingester.cache.AerospikeCacheClient;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.utils.MandateUtility;
import com.org.panaroma.ingester.utils.RetryUtility;
import java.io.Serializable;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class MandateDocTransformer implements FlatMapFunction<TransactionHistoryDetails, MandateBaseDto>, Serializable {

	private final MetricsAgent metricsAgent;

	private final EsRepository esRepository;

	private final AerospikeCacheClient aerospikeCacheClient;

	private final RetryUtility retryUtility;

	// Cached epoch millis for performance
	private final Map<TransactionTypeEnum, Long> goLiveDateCache = new ConcurrentHashMap<>();

	public MandateDocTransformer(final EsRepository esRepository, final AerospikeCacheClient aerospikeCacheClient,
			final MetricsAgent metricsAgent, final RetryUtility retryUtility,
			@Value("${recurring-mandate-history-go-live-date}") final String recurringMandateHistoryGoLiveDate,
			@Value("${ipo-mandate-history-go-live-date}") final String ipoMandateHistoryGoLiveDate,
			@Value("${sbmd-mandate-history-go-live-date}") final String sbmdMandateHistoryGoLiveDate,
			@Value("${one-time-mandate-history-go-live-date}") final String oneTimeMandateHistoryGoLiveDate) {
		this.esRepository = esRepository;
		this.aerospikeCacheClient = aerospikeCacheClient;
		this.metricsAgent = metricsAgent;
		this.retryUtility = retryUtility;

		// Pre-compute epoch millis for better performance
		initializeGoLiveDateCache(recurringMandateHistoryGoLiveDate, ipoMandateHistoryGoLiveDate,
				sbmdMandateHistoryGoLiveDate, oneTimeMandateHistoryGoLiveDate);
	}

	private void initializeGoLiveDateCache(String recurringDate, String ipoDate, String sbmdDate, String oneTimeDate) {
		try {
			goLiveDateCache.put(TransactionTypeEnum.RECURRING_MANDATE,
					DateTimeUtility.getEpochMillis(recurringDate, DATE_FORMAT));
			goLiveDateCache.put(TransactionTypeEnum.IPO_MANDATE, DateTimeUtility.getEpochMillis(ipoDate, DATE_FORMAT));
			goLiveDateCache.put(TransactionTypeEnum.SBMD_MANDATE,
					DateTimeUtility.getEpochMillis(sbmdDate, DATE_FORMAT));
			goLiveDateCache.put(TransactionTypeEnum.ONE_TIME_MANDATE,
					DateTimeUtility.getEpochMillis(oneTimeDate, DATE_FORMAT));
		}
		catch (Exception e) {
			log.error(
					"Failed to initialize go-live date cache. Recurring: {}, IPO: {}, SBMD: {}, OneTime: {}. Exception: {}",
					recurringDate, ipoDate, sbmdDate, oneTimeDate, CommonsUtility.exceptionFormatter(e));
			throw new RuntimeException(
					"Failed to initialize go-live date cache. Please check date format configuration.", e);
		}
	}

	@Override
	public void flatMap(final TransactionHistoryDetails thd, final Collector<MandateBaseDto> collector) {
		logEventReceived(thd);

		if (MandateUtility.isForMandateBackFilling(thd)) {
			metricsAgent.incrementCount(MANDATE_BACK_FILLING_EVENT_COUNT);
		}

		Set<MandateBaseDto> mandateDocs = new HashSet<>();

		try {
			MandateActivityData mandateActivityData = MandateUtility.mapThdToMandateActivityData(thd);
			mandateDocs.add(mandateActivityData);

			MandateActionEnum mandateAction = MandateActionEnum
				.getMandateActionEnumByKey(mandateActivityData.getAction());

			processMandateAction(thd, mandateAction, mandateActivityData, mandateDocs);
		}
		catch (Exception exception) {
			log.error("Exception occurred while transforming THD to Mandate doc for thd: {}. Exception: {}", thd,
					CommonsUtility.exceptionFormatter(exception));
			metricsAgent.incrementCount(EXCEPTION_WHILE_CREATING_MANDATE_DOC);
		}

		mandateDocs.forEach(collector::collect);
	}

	private void logEventReceived(TransactionHistoryDetails thd) {
		String rrn = extractRrn(thd);
		log.warn("{} event received in mandate pipeline with systemId: {}, umn: {} & rrn: {}", thd.getTxnType(),
				thd.getSystemId(), thd.getUmn(), rrn);
	}

	private String extractRrn(TransactionHistoryDetails thd) {
		Map<String, String> contextMap = thd.getContextMap();
		return contextMap != null ? contextMap.get(RRN) : null;
	}

	private void processMandateAction(TransactionHistoryDetails thd, MandateActionEnum mandateAction,
			MandateActivityData mandateActivityData, Set<MandateBaseDto> mandateDocs) {

		if (MandateActionEnum.CREATE.equals(mandateAction)) {
			handleCreateAction(thd, mandateDocs);
		}
		else if (MandateActionEnum.IN_PORT.equals(mandateAction)) {
			handlePortInAction(thd, mandateActivityData, mandateDocs);
		}
		else if (MandateActionEnum.OUT_PORT.equals(mandateAction)) {
			handlePortOutAction(thd, mandateActivityData, mandateDocs);
		}
		else if (needToCheckForParent(thd)) {
			handleOtherActions(thd, mandateActivityData, mandateDocs);
		}
	}

	private void handleCreateAction(TransactionHistoryDetails thd, Set<MandateBaseDto> mandateDocs) {
		MandateInfoData mandateInfoData = MandateUtility.mapThdToMandateInfoData(thd);
		mandateDocs.add(mandateInfoData);
	}

	private void handlePortInAction(TransactionHistoryDetails thd, MandateActivityData mandateActivityData,
			Set<MandateBaseDto> mandateDocs) {
		MandateInfoData parentDoc = getParentDocWithRetry(thd, mandateActivityData, PURPOSE_IN_PORT);
		if (parentDoc == null) {
			log.info("No parent found for Port-In event, treating as CREATE event. UMN: {}, systemId: {}", thd.getUmn(),
					thd.getSystemId());
			parentDoc = MandateUtility.mapThdToMandateInfoData(thd);
			mandateDocs.add(parentDoc);
		}
		else {
			// Parent exists - validate
			// Status updates will be handled by mandateEnricher
			if (!validatePortInStatus(parentDoc, thd)) {
				return;
			}
		}
	}

	private void handlePortOutAction(TransactionHistoryDetails thd, MandateActivityData mandateActivityData,
			Set<MandateBaseDto> mandateDocs) {
		MandateInfoData parentDoc = getParentDocWithRetry(thd, mandateActivityData, PURPOSE_OUT_PORT);
		if (parentDoc != null) {
			// Parent exists - validate
			// Status updates will be handled by mandateEnricher
			if (!validatePortOutStatus(parentDoc, thd)) {
				return;
			}
		}
		else {
			// Critical issue: Parent document missing for OUT_PORT event
			// This indicates data integrity problem - mandate should exist in our system
			log.error(
					"CRITICAL: Parent doc not found for Port-Out event. UMN: {}, systemId: {}. Creating parent document to prevent API failures.",
					thd.getUmn(), thd.getSystemId());
			metricsAgent.incrementCount(PARENT_DOC_MISSING_FOR_PORT_OUT_EVENT);

			// TODO: Set up alerts on PARENT_DOC_MISSING_FOR_PORT_OUT_EVENT metric
			// This indicates data integrity issues that need immediate attention

			// Create parent document to prevent /mandate/history API failures
			parentDoc = MandateUtility.mapThdToMandateInfoData(thd);
			mandateDocs.add(parentDoc);
		}
	}

	private void handleOtherActions(TransactionHistoryDetails thd, MandateActivityData mandateActivityData,
			Set<MandateBaseDto> mandateDocs) {
		MandateInfoData parentDoc = getParentDocWithRetry(thd, mandateActivityData, "Other");
		if (parentDoc == null) {
			parentDoc = MandateUtility.mapThdToMandateInfoData(thd);
			mandateDocs.add(parentDoc);
		}
		else if (parentDoc.getStatus() == null) {
			updateParentDocStatus(thd, parentDoc, mandateDocs);
		}
	}

	private MandateInfoData getParentDocWithRetry(TransactionHistoryDetails thd,
			MandateActivityData mandateActivityData, String operationType) {
		try {
			return getMandateParentDoc(mandateActivityData);
		}
		catch (Exception e) {
			log.error("Exception occurred while getting parent doc for {} event. thd: {}. Exception: {}", operationType,
					thd, CommonsUtility.exceptionFormatter(e));
			metricsAgent.incrementCount(EXCEPTION_WHILE_GETTING_PARENT_MANDATE_DOC);
			retryUtility.pushMandateDataThdToKafka(thd);
			return null;
		}
	}

	private boolean validatePortInStatus(MandateInfoData parentDoc, TransactionHistoryDetails thd) {
		// Only validate if status is not null (i.e., mandate is already in Paytm
		// ecosystem)
		if (parentDoc.getStatus() != null) {
			MandateStatusEnum currentStatus = MandateStatusEnum.getStatusEnumByKey(parentDoc.getStatus());
			if (!MandateStatusEnum.PORTED.equals(currentStatus)) {
				log.warn(
						"Port-In event for existing mandate rejected - mandate must be PORTED for porting. "
								+ "Current status: {}, UMN: {}, systemId: {}",
						currentStatus, thd.getUmn(), thd.getSystemId());
				return false;
			}
		}
		// Allow null status (pending/failed IN_PORT events where mandate is not yet in
		// Paytm ecosystem)
		return true;
	}

	private boolean validatePortOutStatus(MandateInfoData parentDoc, TransactionHistoryDetails thd) {
		if (parentDoc.getStatus() != null) {
			MandateStatusEnum currentStatus = MandateStatusEnum.getStatusEnumByKey(parentDoc.getStatus());
			if (!MandateStatusEnum.ACTIVE.equals(currentStatus)) {
				log.warn(
						"Port-Out event rejected - mandate must be ACTIVE for porting. "
								+ "Current status: {}, UMN: {}, systemId: {}",
						currentStatus, thd.getUmn(), thd.getSystemId());
				return false;
			}
		}
		return true;
	}

	private void updateParentDocStatus(TransactionHistoryDetails thd, MandateInfoData parentDoc,
			Set<MandateBaseDto> mandateDocs) {
		MandateStatusEnum statusEnum = MandateUtility.getMandateStatus(thd);
		if (statusEnum != null) {
			parentDoc.setStatus(statusEnum.getMandateStatusKey());
			parentDoc.setUpdatedDate(Long.parseLong(thd.getUpdatedDate()));
			parentDoc.setDocUpdatedDate(System.currentTimeMillis());

			if (MandateUtility.isForMandateBackFilling(thd)) {
				MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
			}
			mandateDocs.add(parentDoc);
		}
	}

	public MandateInfoData getMandateParentDoc(final MandateActivityData mandateActivityData) throws Exception {
		String cacheKeyForParent = mandateActivityData.getEntityId() + PIPE_SYMBOL + mandateActivityData.getUmn();
		MandateInfoData parentDoc = (MandateInfoData) aerospikeCacheClient.getStoredMandateData(cacheKeyForParent,
				new TypeReference<MandateInfoData>() {
				});
		if (Objects.isNull(parentDoc)) {
			parentDoc = (MandateInfoData) esRepository.getMandateData(mandateActivityData, true, false);
		}
		return parentDoc;
	}

	public boolean needToCheckForParent(final TransactionHistoryDetails thd) {
		Map<String, String> contextMap = thd.getContextMap();
		if (ObjectUtils.isEmpty(contextMap) || !contextMap.containsKey(VALIDITY_START_DATE)) {
			return true;
		}

		TransactionTypeEnum txnType = thd.getTxnType();
		Long goLiveDate = goLiveDateCache.get(txnType);

		if (goLiveDate != null) {
			try {
				long validityStartDate = Long.parseLong(contextMap.get(VALIDITY_START_DATE));
				return goLiveDate > validityStartDate;
			}
			catch (NumberFormatException e) {
				log.warn("Invalid validity start date format for UMN: {}, systemId: {}", thd.getUmn(),
						thd.getSystemId());
				return true;
			}
		}

		return false;
	}

}

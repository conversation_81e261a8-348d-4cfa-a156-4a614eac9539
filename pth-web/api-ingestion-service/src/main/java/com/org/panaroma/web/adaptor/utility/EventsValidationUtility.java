package com.org.panaroma.web.adaptor.utility;

import static com.org.panaroma.commons.constants.CommonConstants.MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_UPDATE;
import static com.org.panaroma.commons.constants.WebConstants.PURPOSE_CODE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_EXPIRE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.WebConstants.MANDATE_STATUS;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.TXN_TYPE;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.EXPIRE_NON_SUCCESS_EVENTS_FILTER;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.MANDATE_PORT_IN_VALIDATION_FAILED;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.MANDATE_STATUS_MISSING;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.PORT_TYPE;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.MANDATE_PORT_VALIDATION_FAILED;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.adaptor.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.adaptor.monitoring.MetricsAgent;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Log4j2
@Component
public class EventsValidationUtility {

	@Autowired
	private MetricsAgent metricsAgent;

	public boolean isUpiEventNeedToBeDiscarded(final TransactionHistoryDetails data) {

		// Check for SBMD_MANDATE with PURPOSE_UPDATE
		if (TransactionTypeEnum.SBMD_MANDATE.getTransactionTypeKey().equals(data.getTxnType().getTransactionTypeKey())
				&& PURPOSE_UPDATE.equalsIgnoreCase(Utility.getPurpose(data))) {
			log.info("Discarded SBMD_MANDATE UPDATE event. systemId: {}", data.getSystemId());
			return true;
		}

		// Check for expire non-success events
		return isBlacklistedMandateExpireEvent(data);
	}

	/**
	 * Validates Port-In/Port-Out mandate events with purpose codes and mandate status
	 * @param data The transaction history details
	 * @return true if the port event is valid for processing
	 * @throws AdaptorException if validation fails
	 */
	public boolean isValidPortEvent(final TransactionHistoryDetails data) {
		String txnPurpose = Utility.getPurpose(data);
		String purpose = data.getContextMap() != null ? data.getContextMap().get(PURPOSE_CODE) : null;

		// Validate Port-In events (purpose: BC for port, AZ for interoperability)
		if (PURPOSE_IN_PORT.equalsIgnoreCase(txnPurpose)) {
			if (PURPOSE_BC.equalsIgnoreCase(purpose) || PURPOSE_AZ.equalsIgnoreCase(purpose)) {
				log.info("Valid Port-In event detected. systemId: {}, purpose: {}", data.getSystemId(), purpose);

				// Validate mandate status for Port-In events
				if (!validateMandateStatusForPortIn(data)) {
					return false;
				}

				return true;
			}
			else {
				log.warn("Invalid Port-In event - missing valid purpose code (BC/AZ). systemId: {}, purpose: {}",
						data.getSystemId(), purpose);
				return false;
			}
		}

		// Validate Port-Out events (purpose: BC for port, AZ for interoperability)
		if (PURPOSE_OUT_PORT.equalsIgnoreCase(txnPurpose)) {
			if (PURPOSE_BC.equalsIgnoreCase(purpose) || PURPOSE_AZ.equalsIgnoreCase(purpose)) {
				log.info("Valid Port-Out event detected. systemId: {}, purpose: {}", data.getSystemId(), purpose);

				// For Port-Out, we don't need to validate mandate status as per
				// requirements
				// If we got OUT_PORT SUCCESS event, that 100% means the state is PORTED
				return true;
			}
			else {
				log.warn("Invalid Port-Out event - missing valid purpose code (BC/AZ). systemId: {}, purpose: {}",
						data.getSystemId(), purpose);
				return false;
			}
		}

		return true; // Not a port event, allow normal processing
	}

	/**
	 * Validates mandate status for Port-In events For Port-In events: mandate status must
	 * be ACTIVE
	 * @param data The transaction history details
	 * @return true if validation passes, false otherwise
	 */
	private boolean validateMandateStatusForPortIn(final TransactionHistoryDetails data) {
		Map<String, String> contextMap = data.getContextMap();
		if (Objects.isNull(contextMap)) {
			log.error("Port-In event rejected - context map is null. UMN: {}, systemId: {}", data.getUmn(),
					data.getSystemId());
			metricsAgent.incrementCount(MANDATE_STATUS_MISSING, PORT_TYPE + COLON + PURPOSE_IN_PORT,
					TXN_TYPE + COLON + data.getTxnType());
			throw ExceptionFactory.getException("ADAPTOR_SERVICE", MANDATE_PORT_VALIDATION_FAILED);
		}

		String mandateStatus = contextMap.get(MANDATE_STATUS);
		if (StringUtils.isBlank(mandateStatus)) {
			log.error("Port-In event rejected - mandateStatus is missing in context map. UMN: {}, systemId: {}",
					data.getUmn(), data.getSystemId());
			metricsAgent.incrementCount(MANDATE_STATUS_MISSING, PORT_TYPE + COLON + PURPOSE_IN_PORT,
					TXN_TYPE + COLON + data.getTxnType());
			throw ExceptionFactory.getException("ADAPTOR_SERVICE", MANDATE_PORT_VALIDATION_FAILED);
		}

		if (!MandateStatusEnum.ACTIVE.name().equals(mandateStatus)) {
			log.error(
					"Port-In event rejected - mandateStatus in context map is not ACTIVE. "
							+ "Current mandateStatus: {}, UMN: {}, systemId: {}",
					mandateStatus, data.getUmn(), data.getSystemId());
			metricsAgent.incrementCount(MANDATE_PORT_IN_VALIDATION_FAILED, PORT_TYPE + COLON + PURPOSE_IN_PORT,
					MANDATE_STATUS + COLON + mandateStatus, TXN_TYPE + COLON + data.getTxnType());
			throw ExceptionFactory.getException("ADAPTOR_SERVICE", MANDATE_PORT_VALIDATION_FAILED);
		}

		log.info("Port-In event validation passed - mandateStatus: {}, UMN: {}, systemId: {}", mandateStatus,
				data.getUmn(), data.getSystemId());
		return true;
	}

	/**
	 * Checks if an event is an expire non-success event As per JIRA PTH-1069, we need to
	 * discard expire non-success events for all mandate types
	 * @param data The transaction history details
	 * @return true if the event should be blacklisted, false otherwise
	 */
	private boolean isBlacklistedMandateExpireEvent(final TransactionHistoryDetails data) {
		// First check if the transaction type is a mandate type
		if (!MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES.contains(data.getTxnType())) {
			return false;
		}

		// Check if it's an EXPIRE action
		String txnPurpose = Utility.getPurpose(data);
		if (PURPOSE_EXPIRE.equalsIgnoreCase(txnPurpose)) {
			// Check if it's not a SUCCESS status
			if (!ClientStatusEnum.SUCCESS.equals(data.getStatus())) {
				log.info("Discarded expire non-success event. txnId: {}, txnType: {}, status: {}", data.getSystemId(),
						data.getTxnType(), data.getStatus());

				// Increment metrics for filtered expire events
				metricsAgent.incrementCount(EXPIRE_NON_SUCCESS_EVENTS_FILTER, TXN_TYPE + COLON + data.getTxnType(),
						STATUS + COLON + data.getStatus());

				return true;
			}
		}

		return false;
	}

}
